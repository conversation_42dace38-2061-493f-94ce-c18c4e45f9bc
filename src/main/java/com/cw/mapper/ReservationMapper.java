package com.cw.mapper;

import com.cw.entity.Reservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Classname ReservationMapper
 * @Description 预定映射
 * @Date 2024-03-27 20:40
 * <AUTHOR> sancho.shen
 */
public interface ReservationMapper extends JpaRepository<Reservation, Long>, JpaSpecificationExecutor<Reservation> {


    Reservation findByHotelIdAndProfileNumberAndReservationStatus(String hotelId, String profileNumber, int reservationStatus);

    Reservation findByHotelIdAndRoomNumberAndReservationStatus(String hotelId, String roomNumber, int reservationStatus);

    @Transactional(readOnly = true)
    Reservation findByHotelIdAndReservationNumber(String hotelId, String reservationNumber);

    int countByHotelIdAndReservationType(String hotelId, String reservationType);

    //int countReservationByOccRoom();

    @Transactional(readOnly = true)
    @Query(value = "from Reservation where reservationNumber  in(?1) and hotelId=?2")
    List<Reservation> findByReservationNumbers(List<String> reservationNumbers, String hotelId);


    @Query(value = "from Reservation where relationNumber  =?1 and reservationNumber<>?2 and hotelId =?3")
    List<Reservation> findMyLinkRs(String relationNumber, String rsno, String hotelId);

    /**
     * @param rsStatus    订单预抵和在住状态
     * @param arrivalDate 到店日期
     * @param hotelId     酒店代码
     * @return 今日预抵订单 或者在住订单
     */
    @Query(value = "from Reservation" +
            " where reservationStatus in ?1 and  arrivalDate <= ?2 and departureDate >=?2  and hotelId=?3   order by  arrivalDate")
    List<Reservation> getTodayRsRoomData(Integer rsStatus, Date arrivalDate, String hotelId);

    /**
     * @param hotelId        酒店代码
     * @param EXPECTEDStatus 预抵状态
     * @param todayDate      当日日期
     * @param checkinStatus  在住状态
     * @return 今日房间统计 返回 包括预抵和在住的订单，在住订单排除预离
     */
    @Query(value = "from Reservation where" +
            " hotelId=?1 and ((reservationStatus=?2 AND arrivalDate=?3)  OR   (reservationStatus=?4 AND departureDate>?3)) ")
    List<Reservation> getTodayRoomRsData(String hotelId, Integer EXPECTEDStatus, Date todayDate, Integer checkinStatus);


    @Query("SELECT r.reservationNumber FROM Reservation r " +
            "WHERE r.roomNumber = ?1 AND r.hotelId=?5   " +
            "AND r.reservationNumber != ?2 " +
            "AND r.reservationStatus IN (0, 1) " +  // 预订, 在住, 应离未离
            "AND ((r.arrivalDate < ?4 AND r.departureDate >?3) " +
            "     OR (r.arrivalDate > ?3 AND r.arrivalDate < ?4))")
    List<String> fetchRoomOccupiedRs(String roomNumber,
                                     String currentReservationNumber,
                                     Date arrivalDate,
                                     Date departureDate, String hotelId);

    /**
     * 统计今日销售额=房价总额（备注：今日创建的订单，且订单状态0和1的销售额）
     *
     * @param hotelId 酒店id
     * @return 今日销售额
     */
    @Query(value = "SELECT SUM(total_price) " +
            "FROM reservation " +
            "WHERE hotelid = ?1 " +
            "AND reservation_status in(0,1) " +
            "AND create_time >= CURDATE() " +
            "AND create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY)", nativeQuery = true)
    BigDecimal sumTodaySales(String hotelId);

    /**
     * 统计今日预抵订单数量（备注：预抵订单为订单状态0，且入住日期为当天）
     *
     * @param hotelId  酒店id
     * @param rsStatus 订单状态
     * @return 今日预抵订单数量
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM reservation " +
            "WHERE hotelid = ?1 " +
            "AND reservation_status = ?2 " +
            "AND arrival_date >= CURDATE() " +
            "AND arrival_date < DATE_ADD(CURDATE(), INTERVAL 1 DAY)", nativeQuery = true)
    int countTodayArrivalRs(String hotelId, Integer rsStatus);


    /**
     * 统计今日预离订单数量（备注：预离订单为订单状态1，且退房日期为当天）
     * @param hotelId 酒店id
     * @param rsStatus 订单状态
     * @return 今日预离订单数量
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM reservation " +
            "WHERE hotelid = ?1 " +
            "AND reservation_status = ?2 " +
            "AND departure_date >= CURDATE() " +
            "AND departure_date < DATE_ADD(CURDATE(), INTERVAL 1 DAY)", nativeQuery = true)
    int countTodayLeaveRs(String hotelId, Integer rsStatus);

    /**
     * 统计今日订单数量（备注：今日订单为今日创建的订单数量）
     * @param hotelId 酒店id
     * @return 今日订单数量
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM reservation " +
            "WHERE hotelid = ?1 " +
            "AND create_time >= CURDATE() " +
            "AND create_time < DATE_ADD(CURDATE(), INTERVAL 1 DAY)", nativeQuery = true)
    int countTodayRs(String hotelId);

    List<Reservation> findByRelationNumberAndHotelId(String relationNumber, String hotelId);

    List<Reservation> findByHotelIdAndSuino(String hotelId, String Suino);
}
