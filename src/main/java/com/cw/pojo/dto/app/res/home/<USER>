package com.cw.pojo.dto.app.res.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Classname HomeHotelInfo
 * @Description 首页酒店信息
 * @Date 2024-04-10 21:46
 * <AUTHOR> sancho.shen
 */
@ApiModel(value = "首页酒店信息")
@Data
public class HomeHotelInfoRes implements Serializable {
    @ApiModelProperty(value = "酒店名称")
    private String hotelName;
    @ApiModelProperty(value = "酒店id")
    private String hotelId;
    @ApiModelProperty(value = "酒店协议条款")
    private String agreement;
}
