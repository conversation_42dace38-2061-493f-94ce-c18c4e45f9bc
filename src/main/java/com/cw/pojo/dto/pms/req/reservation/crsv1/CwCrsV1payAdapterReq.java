package com.cw.pojo.dto.pms.req.reservation.crsv1;

import cn.hutool.core.util.StrUtil;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.utils.enums.AccountItemEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/28 12:03
 **/
public class CwCrsV1payAdapterReq {

    private final String hotelId;
    private final CwCrsColPayV1Req req;

    public CwCrsV1payAdapterReq(String hotelId, CwCrsColPayV1Req req) {
        this.hotelId = hotelId;
        this.req = req;
    }


    public CashierPostReq toCashierPostReq(String ar_no, String roomReservationNo) {
        //
        CashierPostReq cashierPostReq = new CashierPostReq();
        cashierPostReq.setReservationNumber(roomReservationNo);
        List<CashierAccountReq> lis = new ArrayList<>();
        CashierAccountReq cashierAccountReq = new CashierAccountReq();
        cashierAccountReq.setDepartmentCode(req.getDeptCode());
        cashierAccountReq.setDescription("");
        cashierAccountReq.setCredit(req.getAmount());//2025.1.14
        cashierAccountReq.setRemark(StrUtil.EMPTY);
        cashierAccountReq.setAr_no(StrUtil.EMPTY);
        cashierAccountReq.setTradeno(req.getSerialNo());
        cashierAccountReq.setAr_no(ar_no);

        lis.add(cashierAccountReq);
        cashierPostReq.setAccounts(lis);

        return cashierPostReq;
    }
}
