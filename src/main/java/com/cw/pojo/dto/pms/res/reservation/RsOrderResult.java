package com.cw.pojo.dto.pms.res.reservation;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/7 11:21
 **/
@Data
@ApiModel(description = "新建订单返回")
public class RsOrderResult {

    //@ApiModelProperty(value = "综合预订号-多个订单时的关系订单号", example = "20240182745", required = true)
    //String colno;

    @ApiModelProperty(value = "客房订单号", example = "20240182745", required = true)
    String reservaitonNumber;



    @ApiModelProperty(value = "主单号-使用主单号可以查出所有子单", example = "20240182745", required = true)
    String relationNumber;

    @ApiModelProperty(value = "综合预订号")
    String colno;

    @ApiModelProperty(value = "订单总价", example = "100.00")
    BigDecimal totalAmount;

    @ApiModelProperty(value = "实际待支付金额-为0时可跳过支付", example = "100.00")
    BigDecimal actualAmount;


    @ApiModelProperty(value = "客房ID 集合")
    List<IdResult> roomIds = Lists.newArrayList();

    List<IdResult> profileIds = Lists.newArrayList();

    IdResult colIds = null;


    @Data
    public static class IdResult {
        private String pmsid;
        private String outid;

        public IdResult(String pmsid, String outid) {
            this.pmsid = pmsid;
            this.outid = outid;
        }
    }


}
