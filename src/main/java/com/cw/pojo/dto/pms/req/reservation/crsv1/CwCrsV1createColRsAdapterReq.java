package com.cw.pojo.dto.pms.req.reservation.crsv1;

import com.cw.entity.Colrs;
import com.cw.entity.Profile;
import com.cw.entity.Reservation;
import com.cw.pms.model.GuestInfo;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pojo.common.core.StdOrderRequest;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/19 15:18
 **/
@Data
@ApiModel(description = "CRS1.0创建接待单请求")
public class CwCrsV1createColRsAdapterReq implements StdOrderRequest {
    private final CwCrsColRsSaveV1Req req;
    private final String hotelId;
    private final Colrs colrs;

    public CwCrsV1createColRsAdapterReq(CwCrsColRsSaveV1Req req, String hotelId, Colrs colrs) {
        this.req = req;
        this.hotelId = hotelId;
        this.colrs = colrs;
    }

    @Override
    public Colrs getColrs() {
        return colrs;
    }

    @Override
    public List<Reservation> getRoomRss() {
        List<Reservation> reservations = new ArrayList<>();
        for (CwCrsColRsSaveV1Req.Room room : req.getRooms()) {
            Reservation reservation = new Reservation();
            reservation.setHotelId(hotelId);
            reservation.setArrivalDate(room.getStayDateRange().getStartDate());
            reservation.setDepartureDate(room.getStayDateRange().getEndDate());
            reservation.setRoomType(room.getRoomType());
            reservation.setOtano(colrs.getOtano());
            reservation.setCrsno(room.getConfirmRoomNumber());
            reservation.setBlock(room.getBlockCode());
            reservation.setRooms(room.getNumberOfRooms());
            reservation.setRateCode(room.getRateCode());
            reservation.setChannel(room.getChannel());

            reservations.add(reservation);
        }


        return reservations;
    }

    @Override
    public Profile getMainProfile() {
        GuestInfo guestInfo = req.getGuestInfo();
        Profile profile = new Profile();
        profile.setGuestName(guestInfo.getName());
        profile.setTelephone(guestInfo.getMobile());
        profile.setGender(guestInfo.getGender().equals("M") ? 1 : 0);

        profile.setHotelId(hotelId);
        return profile;
    }

    @Override
    public String getHotelId() {
        return hotelId;
    }
}

