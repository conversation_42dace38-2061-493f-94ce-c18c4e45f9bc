package com.cw.utils.options;


public enum Options {
    AUTO_ASSROOM("OTA下单自动分房", OptionsGroup.ORDER, true, 1), //这个默认打开
    CI_AUTODI(" 入住自动变脏房", OptionsGroup.ORDER, false, 4),

    SALER("订单信息-销售", OptionsGroup.ORDER,true, 2),//保洁员
    FORBID_CHECKIN_IN_DIRTY_ROOM("脏房不允许办理入住", OptionsGroup.ORDER, true, 6),
    CHECKOUT_PENDING_PROHIBIT_CHECKIN("预离房不允许办理入住", OptionsGroup.ORDER, true, 7),
    STAYOVER_DAILY_DIRTY("连住房自动置为脏房", OptionsGroup.ORDER, true, 8),



    ;

    private String desc;
    private OptionsGroup group;
    private Boolean defaultValue;
    private Integer sortIndex; //同一组里按从小到大排序
    private String val = "";

    Options(String desc, OptionsGroup group, Boolean defaultValue, Integer sortIndex, String... val) {
        this.desc = desc;
        this.group = group;
        this.defaultValue = defaultValue;
        this.sortIndex = sortIndex;
        if (val != null && val.length > 0) {
            this.val = val[0];
        }
    }

    public String getVal() {
        return val;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean getDefaultValue() {
        return defaultValue;
    }

    public Integer getSortIndex() {
        return sortIndex;
    }

    public OptionsGroup getGroup() {
        return group;
    }

}
