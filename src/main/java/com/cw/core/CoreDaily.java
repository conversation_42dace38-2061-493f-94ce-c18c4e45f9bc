package com.cw.core;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.rate.PriceTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.IncludeRateCache;
import com.cw.cache.impl.RoomRateCache;
import com.cw.entity.*;
import com.cw.mapper.Pkg_dailyMapper;
import com.cw.mapper.Rooms_dailyMapper;
import com.cw.pojo.common.PkgNode;
import com.cw.utils.CalculateDate;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.StatusTypeUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/06/26 17:55
 **/
@Service
public class CoreDaily {

    @Autowired
    Rooms_dailyMapper roomsDailyMapper;

    @Autowired
    Pkg_dailyMapper pkgdailyMapper;

    public List<RoomsDaily> getDailys(String regno, String hotelId) {
        return roomsDailyMapper.findRooms_dailiesByRegno(regno, hotelId);
    }

    public List<RoomsDaily> produceDaily(Reservation rs) {
        Date startDate = CalculateDate.maxDate(rs.getArrivalDate(), CalculateDate.getSystemDate());
        int count = CalculateDate.compareDates(rs.getDepartureDate(), startDate).intValue();
        if (count == 0 && CalculateDate.isEqual(rs.getArrivalDate(), rs.getDepartureDate())) {  //如果hoteldate离店的，不会生成
            count++;
        }
        List<RoomsDaily> dailys = new ArrayList<>();
        RoomsDaily daily = null;
        for (int i = 0; i < count; i++) {
            daily = getNewDaily(CalculateDate.reckonDay(startDate, 5, i), rs);
            dailys.add(daily);
        }
        return dailys;
    }

    private RoomsDaily getNewDaily(Date d, Reservation rs) {
        RoomsDaily daily = new RoomsDaily();
        daily.setDatum(d);
        daily.setReservationNumber(rs.getReservationNumber());
        daily.setFixrate(rs.getFixrate());
        daily.setRateCode(rs.getRateCode());
        daily.setHotelId(rs.getHotelId());
        return daily;
    }

    /**
     * 只返回那些对运算有用的daily.不够的就补充.多的不需要的不会加入运算
     *
     * @param orgdailys
     * @param rs
     * @param startDate
     * @return
     */
    public List<RoomsDaily> makeUpDaily(List<RoomsDaily> orgdailys, Reservation rs, Date startDate) {
        int length = CalculateDate.compareDates(rs.getDepartureDate(), rs.getArrivalDate()).intValue();
        HashMap<Date, RoomsDaily> hms = new HashMap<>();
        for (RoomsDaily d : orgdailys) {
            hms.put(CalculateDate.reckonDay(d.getDatum(), 5, 0), d);  //这样是为了保证KEY的DATE类型一致
        }
        List<RoomsDaily> result = new ArrayList<>();
        if (length == 0) {
            length++;
        }
        Date checkDate = null;
        RoomsDaily daily = null;
        for (int i = 0; i < length; i++) {  //循环一遍。住店期间没有的，就补
            checkDate = CalculateDate.reckonDay(startDate, 5, i);
            if (!hms.containsKey(checkDate)) {
                daily = getNewDaily(checkDate, rs);
                result.add(daily);
            } else {
                daily = hms.get(checkDate);
                daily.setFixrate(rs.getFixrate());
                daily.setRateCode(rs.getRateCode());
                result.add(daily);
            }
        }
        return result;
    }

    public List<RoomsDaily> updateRsDailys(Reservation rs, List<RoomsDaily> dailyList) {
        List<RoomsDaily> returns = new ArrayList<>();
        for (RoomsDaily daily : dailyList) {
            daily.setReservationNumber(rs.getReservationNumber());
            daily.setRateCode(rs.getRateCode());
        }
        Date hotelDate = CalculateDate.getSystemDate();
        for (RoomsDaily daily : dailyList) {
            if (CalculateDate.isBefore(daily.getDatum(), hotelDate)) {  //预定是系统时间之前到店的话，只保存还有用的DAILY。
                if (daily.getId() == null) {  //
                    daily = roomsDailyMapper.saveAndFlush(daily);
                    returns.add(daily);
                } else {
                    returns.add(daily);
                }
            } else {  //预定今天或以后到店。DAILY全部保存
                if (CalculateDate.isEqual(rs.getArrivalDate(), rs.getDepartureDate())) {
                    daily = roomsDailyMapper.saveAndFlush(daily);
                    returns.add(daily);
                } else {  //不是DAYUSE的话以后到店的记录都做删除
                    if (!CalculateDate.afterEqual(daily.getDatum(), rs.getDepartureDate())) { //如果是到店之后的DAILY就删除
                        daily = roomsDailyMapper.saveAndFlush(daily);
                        returns.add(daily);
                    }
                }
            }
        }
        adjustRsDaily(rs);  //删除多余的DAILY
        return returns;
    }

    private void adjustRsDaily(Reservation rs) {
        Date hotelDate = CalculateDate.returnDate_ZeroTime(new Date());
        if (CalculateDate.isAfter(rs.getArrivalDate(), hotelDate)) { //是改成系统日期之后到的话
            if (CalculateDate.isEqual(rs.getArrivalDate(), rs.getDepartureDate())) {
                roomsDailyMapper.deleteAllByAdjustAfter(rs.getReservationNumber(), rs.getArrivalDate(), CalculateDate.reckonDay(rs.getDepartureDate(), 5, 1), rs.getHotelId());
            } else {
                roomsDailyMapper.deleteAllByAdjustAfter(rs.getReservationNumber(), rs.getArrivalDate(), rs.getDepartureDate(), rs.getHotelId());
            }
        } else if (CalculateDate.beforeEqual(rs.getArrivalDate(), hotelDate)) {
            if (CalculateDate.isEqual(rs.getArrivalDate(), rs.getDepartureDate())) {
                roomsDailyMapper.deleteAllByAdjustBefore(rs.getReservationNumber(), CalculateDate.reckonDay(rs.getDepartureDate(), 5, 1), rs.getHotelId());
            } else {
                roomsDailyMapper.deleteAllByAdjustBefore(rs.getReservationNumber(), rs.getDepartureDate(), rs.getHotelId());
            }
        }
    }

    public List<RoomsDaily> copyRsDailys(Date startDate, String oldRegi, String newRegi, String hotelId, Reservation newRs) {
        List<RoomsDaily> lis = roomsDailyMapper.findRooms_dailiesByRegno(oldRegi, hotelId);

        Date checkDate = CalculateDate.maxDate(startDate, CalculateDate.getSystemDate());
        List<RoomsDaily> newLis = new ArrayList<RoomsDaily>();
        for (RoomsDaily rsDaily : lis) {
            if (!CalculateDate.isBefore(rsDaily.getDatum(), checkDate)) {
                RoomsDaily newD = PojoUtils.cloneEntity(rsDaily, false);
                newD.setReservationNumber(newRegi);
                newD.setHotelId(hotelId);
                newD = roomsDailyMapper.save(newD);
                newRs.setTotalPrice(newRs.getTotalPrice().add(newD.getPrice()));
                newLis.add(newD);
            }
        }
        return newLis;
    }

    public void writePkgDaily(Reservation roomRs) {
        List<PkgDaily> existingPkgDailies = Lists.newArrayList(); // 已存在的包价
        List<PkgDaily> deletePkgDailies = Lists.newArrayList();  // 待删除的包价

        if (roomRs.getId() != null && roomRs.getId() > 0) {
            existingPkgDailies = pkgdailyMapper.findByHotelIdAndReservationNumber(roomRs.getHotelId(), roomRs.getReservationNumber());
        }

        // 获取房价信息
        RoomRateCache rateCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMRATE);
        RoomRate roomRate = rateCache.getRecord(roomRs.getHotelId(), roomRs.getRateCode());

        // 获取包价费用配置
        IncludeRateCache includeRateCache = GlobalCache.getDataStructure().getCache(GlobalDataType.INCLUDERATE);

        // 合并房价包含的服务和预订包含的服务
        List<String> calcPkgs = Stream.of(roomRate.getIncludeCode(), roomRs.getPkgs())
                .filter(StrUtil::isNotBlank)  // 过滤掉 null 和空字符串
                .collect(Collectors.toList());

        // 创建最新计算出的服务包节点
        List<PkgNode> pkgNodes = PriceTool.calculatePackages(calcPkgs, null);

        // 计算开始日期（取今天和入住日期的较大值）
        Date hotelDate = CalculateDate.getSystemDate();
        Date calcStart = CalculateDate.maxDate(hotelDate, roomRs.getArrivalDate());
        int days = CalculateDate.compareDates(roomRs.getDepartureDate(), calcStart).intValue();
        if (days == 0) {
            days++;
        }

        List<PkgDaily> toSave = new ArrayList<>();

        // 遍历每一天
        for (int i = 0; i < days; i++) {
            Date currentDate = CalculateDate.reckonDay(calcStart, 5, i);

            // 对每个服务节点处理
            for (PkgNode pkgNode : pkgNodes) {
                // 查找对应的服务配置
                IncludeRate rate = includeRateCache.getRecord(roomRs.getHotelId(), pkgNode.getPkg());
                if (rate == null) continue;

                // 根据frequency判断是否需要在当前日期生成服务
                boolean shouldGenerate = false;
                if (rate.getFrequency() == StatusTypeUtils.PkgCycle.EVERYDAY) {
                    shouldGenerate = true;
                } else if (rate.getFrequency() == StatusTypeUtils.PkgCycle.ONECE) {
                    shouldGenerate = (i == 0);
                }

                if (shouldGenerate) {
                    // 查找是否存在可复用的记录
                    PkgDaily existingDaily = existingPkgDailies.stream()
                            .filter(d -> d.getPkgcode().equals(rate.getCode())
                                    && CalculateDate.isEqual(d.getDatum(), currentDate))
                            .findFirst()
                            .orElse(null);

                    if (existingDaily != null) {
                        // 更新现有记录
                        existingDaily.setPrice(rate.getPrice());
                        existingDaily.setDepartmentCode(rate.getDepartmentCode());
                        existingDaily.setQuantity(pkgNode.getNum());
                        existingDaily.setUsedate(rate.isNextday() ?
                                CalculateDate.reckonDay(existingDaily.getDatum(), 5, 1) :
                                currentDate);
                        toSave.add(existingDaily);
                        existingPkgDailies.remove(existingDaily);
                    } else {
                        // 创建新记录
                        PkgDaily newDaily = new PkgDaily();
                        newDaily.setHotelId(roomRs.getHotelId());
                        newDaily.setReservationNumber(roomRs.getReservationNumber());
                        newDaily.setPkgcode(rate.getCode());
                        newDaily.setDatum(currentDate);
                        newDaily.setDepartmentCode(rate.getDepartmentCode());
                        newDaily.setUsedate(rate.isNextday() ?
                                CalculateDate.reckonDay(currentDate, 5, 1) :
                                currentDate);
                        newDaily.setPrice(rate.getPrice());
                        newDaily.setQuantity(pkgNode.getNum());
                        newDaily.setPrintsep(rate.isPrintsep());
                        newDaily.setNextday(rate.isNextday());
                        toSave.add(newDaily);
                    }
                }
            }
        }

        // 将所有未被复用的原有记录添加到删除列表中
        deletePkgDailies.addAll(existingPkgDailies);

        // 保存新记录
        for (PkgDaily pkgDaily : toSave) {
            pkgdailyMapper.save(pkgDaily);
        }

        // 删除不需要的记录
        for (PkgDaily pkgDaily : deletePkgDailies) {
            pkgdailyMapper.delete(pkgDaily);
        }
    }
}
