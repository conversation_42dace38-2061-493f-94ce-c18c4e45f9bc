package com.cw.core;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.config.exception.BizException;
import com.cw.config.exception.CustomException;
import com.cw.entity.Colrs;
import com.cw.entity.Reservation;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.crsv1.CwCrsColPayV1Res;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostResp;
import com.cw.pojo.dto.pms.req.reservation.UpdRsParamForm;
import com.cw.pojo.dto.pms.req.reservation.crsv1.CwCrsV1createColRsAdapterReq;
import com.cw.pojo.dto.pms.req.reservation.crsv1.CwCrsV1createRoomAdapterReq;
import com.cw.pojo.dto.pms.req.reservation.crsv1.CwCrsV1payAdapterReq;
import com.cw.pojo.dto.pms.res.reservation.RsOrderResult;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.context.GlobalContext;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.SystemUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/25 15:36
 **/
@Service
public class CoreCrsV1 {

    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    private SeqNoService seqNoService;

    @Autowired
    private CoreRs coreRs;

    @Autowired
    CashierService cashierService;


    public CwCrsColRsSaveV1Res saveCrsColRsV1(CwCrsColRsSaveV1Req req) {
        boolean lnew = false;
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        Colrs colrs = daoLocal.getObject("from Colrs c where c.crsno = ?1 and c.hotelId = ?2", req.getCrsColId(), hotelId);
        if (colrs == null) {
            lnew = true;
            colrs = new Colrs();
            colrs.setCrsno(req.getCrsColId());
            colrs.setOtano(req.getOtaOrderId());
            colrs.setChannel(req.getChannelCode());  //是否加对应的转换表
            colrs.setBookingid(seqNoService.getSequenceID(SystemUtil.SequenceKey.ORDERID));
            colrs.setHotelId(hotelId);
        }

        colrs.setTelephone(req.getPhone());
        colrs.setBookerName(req.getGuestInfo().getName());
        colrs.setArrivalDate(req.getArrDate());
        colrs.setDepartureDate(req.getDeptDate());

        CwCrsColRsSaveV1Res res = new CwCrsColRsSaveV1Res();
        CwCrsColRsSaveV1Res .BizModel bizModel = new CwCrsColRsSaveV1Res.BizModel();

        if (lnew && req.getRooms().size() > 0) {  //一般是 OTA 渠道.直接创建进来.
            CwCrsV1createColRsAdapterReq adapterReq = new CwCrsV1createColRsAdapterReq(req, hotelId, colrs);
            try {
                RsOrderResult createResult=  coreRs.createBatchOrder(adapterReq);
                bizModel.getResults();

            } catch (DefinedException e) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(e.getMessage()));
            }
        } else {//如果是编辑模式.就只保存综合预订接待单就好了
            daoLocal.merge(colrs);
        }


        return res;
    }

    public CwCrsRoomSaveV1Res saveCrsRoomRsV1(CwCrsRoomRsSaveV1Req req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        Colrs colrs = daoLocal.getObject("from Colrs c where c.crsno = ?1 and c.hotelId = ?2", req.getColligateNetworkId(), hotelId);
        if (colrs == null) {
            throw new RuntimeException("没有找到对应的订单");
        }

        boolean lnew = StrUtil.isNotBlank(req.getPmsRoomOrderId());//如果已经拿到了 ai 住的订单号.说明目前是编辑客房预订.
        Reservation reservation = null;
        RsOrderResult orderResult = null;
        CwCrsV1createRoomAdapterReq roomAdapterReq = new CwCrsV1createRoomAdapterReq(req, hotelId, colrs);
        if (lnew) {
            try {
                orderResult = coreRs.createBatchOrder(roomAdapterReq);
            } catch (DefinedException e) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(e.getMessage()));
            }
        } else {
            //reservation = daoLocal.getObject("from Reservation r where r.reservationNo = ?1 and r.hotelId = ?2", req.getPmsRoomOrderId(), hotelId);

            UpdRsParamForm paramForm = roomAdapterReq.getUpdForm();

            try {
                coreRs.changeRsParaSave(paramForm, hotelId, "OTAIFC");
            } catch (DefinedException e) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(e.getMessage()));
            }
        }

        CwCrsRoomSaveV1Res res = new CwCrsRoomSaveV1Res();
        if (orderResult != null) {

        }
        return res;
    }

    /**
     * OTA 接口推送预付款.
     * 取消的时候先调用这个接口
     *
     * @param req
     * @return
     */
    public CwCrsColPayV1Res crsColPayV1(CwCrsColPayV1Req req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        String arno = ""; //TODO  如果是渠道订单. 支持挂应收. 获取这个渠道对应的应收账户配置

        Reservation reservation=daoLocal.getObject("from Reservation r where r.reservationNumber = ?1 and r.hotelId = ?2  and reservationStatus = 0  ",
                req.getPmsRoomOrderId(), hotelId);

        CwCrsV1payAdapterReq adapterReq = new CwCrsV1payAdapterReq(hotelId, req);
        CashierPostReq cashierPostReq = adapterReq.toCashierPostReq(arno, req.getSerialNo());
        String res_no = cashierPostReq.getReservationNumber();
        cashierPostReq.setReservationNumber(reservation.getReservationNumber());




        CashierPostResp cashierPostResp = cashierService.post(hotelId, cashierPostReq, g -> {
            g.setRes_no_org(res_no);
        });

        CwCrsColPayV1Res res = new CwCrsColPayV1Res();
        CwCrsColPayV1Res.BizModel bizModel=new CwCrsColPayV1Res.BizModel();
        if(cashierPostResp.getAcc_ids().size()>0){
            bizModel.setPmsAccountId(cashierPostResp.getAcc_ids().stream().findFirst().get());
            res.setData(bizModel);
        }

        return res;
    }


}
