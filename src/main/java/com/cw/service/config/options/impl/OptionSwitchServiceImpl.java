package com.cw.service.config.options.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.OptionSwitchTool;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.entity.Optionswitch;
import com.cw.entity.Saler;
import com.cw.mapper.OptionsMapper;
import com.cw.mapper.RoomMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.pms.req.option.OptionReq;
import com.cw.pojo.dto.pms.req.others.SalerEntity;
import com.cw.service.config.options.OptionEntity;
import com.cw.service.config.options.OptionSwitchService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.options.Options;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/10 17:11
 **/
@Slf4j
@Service
public class OptionSwitchServiceImpl implements OptionSwitchService {

    @Resource
    private DaoLocal<Optionswitch> daoLocal;

    @Autowired
    private OptionSwitchTool optionSwitchFactory;

    @Resource
    private OptionsMapper optionsMapper;

    private boolean isNew(OptionEntity entity) {
        return entity.getId() == null || entity.getId() <= 0;
    }

    private OptionEntity toDto(Optionswitch record) {
        if (record == null) {
            return null;
        }
        OptionEntity entity = new OptionEntity();
        BeanUtil.copyProperties(record, entity, CopyOptions.create().ignoreNullValue());
        return entity;
    }

    @Override
    public OptionEntity save(OptionEntity entity) {
        String hotelId = GlobalContext.getCurrentHotelId();
        Optionswitch optionswitch = new Optionswitch();
        if (isNew(entity)) {
            Optionswitch option = optionsMapper.findAllByHotelIdAndGroupAndOption(hotelId, entity.getGroup(), entity.getOption());
            if (option != null) {
                throw new BizException("参数已存在,不需要新增");
            }
            BeanUtil.copyProperties(entity, optionswitch);
        } else {
            optionswitch = daoLocal.find(Optionswitch.class, entity.getId());
            if (optionswitch == null) {
                throw new BizException("数据不存在");
            }
            BeanUtil.copyProperties(entity, optionswitch, CopyOptions.create().ignoreNullValue());
        }
        optionswitch.setHotelId(GlobalContext.getCurrentHotelId());

        daoLocal.merge(optionswitch);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.OPTIONS, optionswitch.getHotelId());

        return toDto(optionswitch);
    }

    @Override
    public List<Optionswitch> getOptionData(OptionReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        List<Optionswitch> optionswitches = optionSwitchFactory.getOptionData(hotelId);
        if (CollectionUtil.isEmpty(optionswitches)) {
            String jpql = "from Optionswitch where hotelId=:hotelid ";
            Map<String, Object> queryMap = Maps.newHashMap();
            queryMap.put("hotelid", hotelId);
            if (StrUtil.isNotBlank(req.getGroup())) {
                jpql += " and group=:group ";
                queryMap.put("group", req.getGroup());
            }
            if (StrUtil.isNotBlank(req.getOption())) {
                jpql += " and option=:option ";
                queryMap.put("option", req.getOption());
            }
            optionswitches = daoLocal.getList(jpql, queryMap);
        }

        Map<String, Optionswitch> optionswitchMap = optionswitches.stream().collect(Collectors.toMap(Optionswitch::getOption, o -> o));
        // 补全缺失的选项
        for (Options option : Options.values()) {
            if (StrUtil.isNotBlank(req.getGroup()) && !Objects.equals(option.getGroup().name(),req.getGroup())) {
                // 如果 req.getGroup() 不为空且选项的 group 不匹配，则跳过
                continue;
            }
            if (!optionswitchMap.containsKey(option.name())) {
                Optionswitch newOptionswitch = new Optionswitch();
                newOptionswitch.setId(null);
                newOptionswitch.setGroup(option.getGroup().name());
                newOptionswitch.setDesc(option.getDesc());
                newOptionswitch.setHotelId(hotelId);
                newOptionswitch.setOption(option.name());
                newOptionswitch.setSwitchstatus(option.getDefaultValue());
                newOptionswitch.setVal(option.getVal());
                newOptionswitch.setSortIndex(option.getSortIndex());
                optionswitches.add(newOptionswitch);
            }
        }

        return optionswitches;
    }
}
