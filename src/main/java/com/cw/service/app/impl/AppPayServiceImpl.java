package com.cw.service.app.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.domain.BillAmtVo;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.config.Cwconfig;
import com.cw.core.SeqNoService;
import com.cw.core.handler.order.OrderVendorSwitcher;
import com.cw.core.handler.pay.PayVendorSwitcher;
import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.entity.GuestAccounts;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.dto.app.req.AppOnlinePayReq;
import com.cw.pojo.dto.app.req.AppOrderPayQueryReq;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQrcodePayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostResp;
import com.cw.service.app.AppPayService;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateNumber;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AccountItemEnum;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.cw.utils.enums.pay.PayProcessStatus.PAY_SUCCESS;

/**
 * <AUTHOR>
 * @Descripstion 封装所有用户支付业务相关内容
 * @Create 2021/11/4 15:56
 **/
@Slf4j
@Service
public class AppPayServiceImpl implements AppPayService {

    @Autowired
    SeqNoService seqNoService;


    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    Cwconfig cwconfig;


    @Autowired
    PayVendorSwitcher payVendorSwitcher;

    @Autowired
    CashierService cashierService;


    private StdPayQueryParams createStdQueryPayParams(AppOrderPayQueryReq req, String projectId) {
        StdPayQueryParams params = new StdPayQueryParams();
        params.setProjectId(SystemUtil.CONSOLEHOTELID);   //PMS 平台的微信支付ID 全部走平台的支付通道
        params.setOutTradeNo(req.getOutTradeNo());
        return params;
    }

    private StdPayParams createStdPayParams(List<String> orderids, String openid, String hotelId,
                                            int onlinePayMethod, BigDecimal payAmount, Integer payscene, String qrAuthCode, String deptCode) throws DefinedException {
        StdPayParams stdPayParams = new StdPayParams();


        String orderDesc = "酒店消费"; // ContentCacheTool.getPayOrderDesc(booking_rsList);  TODO  后面更改为deptcode 的描述
        String prepeySeqno = null;

        if (onlinePayMethod == OnlinePayMethod.WX_SCANPAY || onlinePayMethod == OnlinePayMethod.ALI_SCANPAY) {
            prepeySeqno = seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, qrAuthCode);
        } else {
            prepeySeqno = seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
        }
        seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
        Long payexpireTime = payscene == 0 ? 1000 * 60 * 2L : 1000 * 60 * 5L; //扫码器支付.一分钟过期.普通订单支付5分钟过期
        payexpireTime = new Date().getTime() + payexpireTime;

        stdPayParams.setProjectId(SystemUtil.CONSOLEHOTELID);  //目前按服务商模式做支付配置
        stdPayParams.setOrderids(orderids);
        stdPayParams.setOutTradeNo(prepeySeqno);
        stdPayParams.setOrderDesc(orderDesc);
        stdPayParams.setTotalPay(payAmount);
        stdPayParams.setPayerId(openid);
        stdPayParams.setExpireTime(payexpireTime);//getPayExpireTimeStamp(booking_rsList, projectId)
        stdPayParams.setNotifyDomain(cwconfig.getDomain());
        stdPayParams.setPaymode(onlinePayMethod);
        stdPayParams.setPayscene(payscene);
        stdPayParams.setQrAuthCode(qrAuthCode);
        stdPayParams.setDeptCode(deptCode);
        stdPayParams.setHotelId(hotelId);

        return stdPayParams;
    }


    @Override
    public AppQrScanPayRes createWxScanPayOrder(AppOnlinePayReq req) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();

        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                hotelId, OnlinePayMethod.WX_SCANPAY, req.getAmount(), req.getPayscene(), req.getQrAuthCode(), req.getDeptCode());

        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        AppQrScanPayRes appQrcodePayRes = wxpayHandler.wxScanPay(stdPayParams);

        return appQrcodePayRes;
    }

    @Override
    public AppQrScanPayRes createALiScanPayOrder(AppOnlinePayReq req) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();

        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                hotelId, OnlinePayMethod.ALI_SCANPAY, req.getAmount(), req.getPayscene(), req.getQrAuthCode(), req.getDeptCode());

        PayVendorHandler alipayHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        AppQrScanPayRes appQrcodePayRes = alipayHandler.aliScanPay(stdPayParams);

        return appQrcodePayRes;
    }

    @Override
    public AppQueryPayRes queryScanPayOrder(AppOnlinePayReq req) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();
        String prepeySeqno = seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, req.getQrAuthCode());

        StdPayQueryParams params = new StdPayQueryParams();
        params.setProjectId(SystemUtil.CONSOLEHOTELID);  //暂时配置为系统级别的支付
        params.setOutTradeNo(prepeySeqno);
        params.setLscanPay(true);

        AppQueryPayRes result = null;
        String deptCode = StrUtil.EMPTY;
        String remark = StrUtil.EMPTY;
        if (9 == req.getQueryPayMethod()) {//支付宝
            PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
            result = aliPayVendor.queryPay(params);
            deptCode = AccountItemEnum.ALIQR.getCode();
            remark = "支付宝扫码支付";
        } else {
            PayVendorHandler wxPayVendor = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
            result = wxPayVendor.queryPay(params);
            deptCode = AccountItemEnum.WXQR.getCode();
            remark = "微信扫码支付";
        }

        if (result.getScan_status_code() == PAY_SUCCESS) {
            //查询支付表成功
            if (!req.getBillingReg().isEmpty()) {   //如果需要进行入账   TODO 调用一个入账接口服务.

                //
                CashierPostReq cashierPostReq = new CashierPostReq();
                cashierPostReq.setReservationNumber(req.getBillingReg());
                List<CashierAccountReq> lis = new ArrayList<>();
                CashierAccountReq cashierAccountReq = new CashierAccountReq();
                cashierAccountReq.setDepartmentCode(deptCode);
                cashierAccountReq.setDescription(remark);
                cashierAccountReq.setCredit(result.getTotalAmount().negate());//2025.1.14
                cashierAccountReq.setRemark(remark);
                cashierAccountReq.setAr_no(StrUtil.EMPTY);
                cashierAccountReq.setTradeno(prepeySeqno);   //根据扫码器扫到的码生成一个本地流水号
                cashierAccountReq.setQueryPayMethod(req.getQueryPayMethod());
                //TODO  将transid 塞进来

                lis.add(cashierAccountReq);
                cashierPostReq.setAccounts(lis);
                cashierService.post(hotelId, cashierPostReq);

                log.info("进行接口入账操作 流水号{}", result.getTransId());
            }
        }
        return result;
    }

    @Override
    public AppQrcodePayRes createWxNativeOrder(AppOnlinePayReq req) throws DefinedException {
        String hotelId = GlobalContext.getCurrentHotelId();

        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                hotelId, OnlinePayMethod.WX_NATIVE, req.getAmount(), req.getPayscene(), req.getQrAuthCode(), req.getDeptCode());
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        AppQrcodePayRes appQrcodePayRes = wxpayHandler.wxNativeQrcodePay(stdPayParams);
        return appQrcodePayRes;

    }


    /**
     * @param appid
     * @param body
     * @return
     */
    @Override
    public WxNotifyResult handleWxJsApiOrderPayNotify(String appid, String body, HttpServletRequest request) {
        boolean newmode = true;

        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.payCallBack(appid, body, request);
        return wxNotifyResult;
    }


    @Override
    public WxNotifyResult handleWxJsApiRefundNotify(String appid, String body, HttpServletRequest request) {
        boolean newmode = true;
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.refundCallBack(appid, body, request);
        return wxNotifyResult;

    }


    @Override
    public String handleAliPayRefundNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler alipayHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        String str = alipayHandler.refundCallBack(appid, body, request);
        return str;
    }

    @Override
    public String handleAliPassByRefundNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler alipayHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        return alipayHandler.refundAnonymousCallBack(appid, body, request);
    }

    /**
     * @param req
     * @return
     * @throws DefinedException
     */
    @Override
    public AppQueryPayRes queryOrderPayStatus(AppOrderPayQueryReq req) throws DefinedException {
        AppQueryPayRes result = new AppQueryPayRes();
        String hotelId = GlobalContext.getCurrentHotelId();

        StdPayQueryParams payQueryParams = createStdQueryPayParams(req, hotelId);
        if (5 == req.getPayMode() || 3 == req.getPayMode()) {//支付宝
            PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
            result = aliPayVendor.queryPay(payQueryParams);
        } else {
            PayVendorHandler wxPayVendor = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
            result = wxPayVendor.queryPay(payQueryParams);
        }
        return result;
    }


    @Override
    public String createAliQrcodePayOrder(AppOnlinePayReq req) throws DefinedException {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                GlobalContext.getCurrentHotelId(), OnlinePayMethod.ALI_QRCODE, req.getAmount(), req.getPayscene(), req.getQrAuthCode(), req.getDeptCode());
        String qrcode = aliPayVendor.aliQrcodePay(stdPayParams);
        return qrcode;
    }


    @Override
    public String handlerAliPayNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        return aliPayVendor.payCallBack(appid, body, request);
    }


}
