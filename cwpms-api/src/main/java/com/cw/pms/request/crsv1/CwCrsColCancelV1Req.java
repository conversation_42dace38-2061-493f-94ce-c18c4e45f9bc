package com.cw.pms.request.crsv1;

import com.cw.common.annotation.CwApiOperation;
import com.cw.common.request.BaseCwApiRequest;
import com.cw.pms.response.crsv1.CwCrsColCancelV1Res;
import lombok.Data;

/**
 * DSPMS综合预订取消请求参数
 * 用于取消综合预订，包括关联的客房、餐饮、门票等所有预订项目
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@CwApiOperation("/pmsopen/v1/cwcrs/cancelcol")
public class CwCrsColCancelV1Req extends BaseCwApiRequest<CwCrsColCancelV1Res> {

    /**
     * 第三方与PMS交互的综合预订id,由第三方提供
     */
    private String crsColId;

    /**
     * 线下PMS综合预订确认id
     */
    private String pmsColId;

    /**
     * 取消原因代码
     * GUEST_REQUEST: 客人要求取消
     * NO_SHOW: 客人未到店
     * OVERBOOKING: 超售
     * SYSTEM_ERROR: 系统错误
     * PAYMENT_FAILED: 支付失败
     * OTHER: 其他原因
     */
    private String cancelReason;

    /**
     * 取消类型
     * FULL: 全部取消
     * PARTIAL: 部分取消
     */
    private String cancelType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 取消备注
     */
    private String remark;

    /**
     * 是否收取取消费用
     */
    private Boolean chargeCancelFee;

    /**
     * 取消费用金额
     */
    private java.math.BigDecimal cancelFeeAmount;

    /**
     * 取消费用说明
     */
    private String cancelFeeRemark;

    /**
     * 预订人（用于验证）
     */
    private String booker;

    /**
     * 预订电话（用于验证）
     */
    private String phone;

    /**
     * 渠道代码
     */
    private String channelCode;

    /**
     * OTA订单号
     */
    private String otaOrderId;

    @Override
    public Class<CwCrsColCancelV1Res> getResponseClass() {
        return CwCrsColCancelV1Res.class;
    }
}
