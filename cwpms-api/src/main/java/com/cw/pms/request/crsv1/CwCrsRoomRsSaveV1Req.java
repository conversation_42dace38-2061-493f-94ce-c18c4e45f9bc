package com.cw.pms.request.crsv1;

import com.alibaba.fastjson.annotation.JSONField;
import com.cw.common.annotation.CwApiOperation;
import com.cw.common.request.BaseCwApiRequest;
import com.cw.pms.model.GuestInfo;
import com.cw.pms.model.RateDailyNode;
import com.cw.pms.model.StayDateRange;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DSPMS客房预订请求参数
 * 专门用于客房预订的简化接口，支持房间分配、房价计划等功能
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@CwApiOperation("/pmsopen/v1/cwcrs/saveroom")
public class CwCrsRoomRsSaveV1Req extends BaseCwApiRequest<CwCrsRoomSaveV1Res> {

    /**
     * 成人数
     */
    private Integer adults;

    /**
     * Block代码
     */
    private String blockCode;

    /**
     * 预订人电话
     */
    private String bookPhone;

    /**
     * 预订人
     */
    private String booker;

    /**
     * 渠道代码
     */
    private String channel;

    /**
     * 儿童数
     */
    private Integer children;


    /**
     * 客人信息
     */
    private GuestInfo guestInfo;

    /**
     * 楼栋代码
     */
    private String hotelCode;

    /**
     * 市场代码
     */
    private String market;

    /**
     * 房间数量
     */
    private Integer numberOfRooms;

    /**
     * 外部OTA订单号
     */
    private String otaOrderId;

    /**
     * 订单号
     */
    private String pmsRoomOrderId;

    /**
     * crs 客房订单号
     */
    private String crsRoomOrderId;


    /**
     * 第三方与PMS交互的id,由第三方提供
     */
    private String crsColId;


    /**
     * 线下PMS确认id，新建订单为空
     */
    private String pmsColId;

    /**
     * 价格代码
     */
    private String rateCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预订类型
     */
    private String resType;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 房型代码
     */
    private String roomType;

    /**
     * 共享号
     */
    private String shareNo;

    /**
     * 来源代码
     */
    private String source;


    /**
     * 订单状态
     */
    private String status;

    /**
     * 综合预订网络ID
     */
    private String colligateNetworkId;

    /**
     * 综合预订确认ID
     */
    private String colligateId;

    /**
     * 确认房间号
     */
    private String confirmRoomNumber;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 预订类型
     */
    private String bookingType;

    /**
     * 普通备注
     */
    private String common;

    /**
     * 特殊备注
     */
    private String specialCommon;

    /**
     * 是否显示房价
     */
    private boolean rateVisible;

    /**
     * 房价计划列表
     */
    private List<RateDailyNode> rateDailyNode = Lists.newArrayList();

    /**
     * 房价详情列表
     */
    private List<RatePlanDetail> ratePlan = Lists.newArrayList();

    /**
     * 支付信息
     */
    private PaymentInfo paymentInfo;

    /**
     * 服务包列表
     */
    private List<ServicePackage> servicePackages = Lists.newArrayList();

    /**
     * 入住日期范围
     */
    private StayDateRange stayDateRange;

    @Override
    public Class<CwCrsRoomSaveV1Res> getResponseClass() {
        return CwCrsRoomSaveV1Res.class;
    }

    /**
     * 房价详情信息
     */
    @Data
    @NoArgsConstructor
    public static class RatePlanDetail {
        /**
         * 日期
         */
        @JSONField(format = "yyyy-MM-dd")
        private Date date;

        /**
         * 金额
         */
        private BigDecimal amount;
    }

    /**
     * 支付信息
     */
    @Data
    @NoArgsConstructor
    public static class PaymentInfo {
        /**
         * 支付类型
         */
        private String paymentType;
    }

    /**
     * 服务包信息
     */
    @Data
    @NoArgsConstructor
    public static class ServicePackage {
        /**
         * 套餐代码
         */
        private String packageCode;
    }

}
