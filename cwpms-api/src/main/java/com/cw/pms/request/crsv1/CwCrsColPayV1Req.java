package com.cw.pms.request.crsv1;

import com.cw.common.annotation.CwApiOperation;
import com.cw.common.request.BaseCwApiRequest;
import com.cw.pms.response.CwColPayRes;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PMS支付请求
 *
 * <AUTHOR>
 */
@Data
@CwApiOperation("/pmsopen/v1/cwcrs/paycol")
public class CwCrsColPayV1Req extends BaseCwApiRequest<CwColPayRes> {


    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付方式代码
     */
    private String deptCode;

    /**
     * 外部支付流水号,一般是平台的预付款单号
     */
    private String serialNo;

    /**
     * pms 支付流水号.付款时可以为空.如果是退款,则需要传入pms的支付流水号
     */
    private String accid;

    /**
     * 客房订单号
     */
    private String pmsRoomOrderId;


    private String pmsColOrderId;

    @Override
    public Class<CwColPayRes> getResponseClass() {
        return CwColPayRes.class;
    }
}
