package com.cw.pms.request.crsv1;

import com.cw.common.annotation.CwApiOperation;
import com.cw.common.request.BaseCwApiRequest;
import com.cw.pms.response.crsv1.CwCrsRoomCancelV1Res;
import lombok.Data;

/**
 * DSPMS客房预订取消请求参数
 * 用于取消单个客房预订，不影响综合预订中的其他项目
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@CwApiOperation("/pmsopen/v1/cwcrs/cancelroom")
public class CwCrsRoomCancelV1Req extends BaseCwApiRequest<CwCrsRoomCancelV1Res> {

    /**
     * CRS客房订单号
     */
    private String crsRoomOrderId;

    /**
     * PMS客房订单号
     */
    private String pmsRoomOrderId;

    /**
     * 第三方与PMS交互的综合预订id（如果是综合预订的一部分）
     */
    private String crsColId;

    /**
     * 线下PMS综合预订确认id（如果是综合预订的一部分）
     */
    private String pmsColId;

    /**
     * 取消原因代码
     * GUEST_REQUEST: 客人要求取消
     * NO_SHOW: 客人未到店
     * OVERBOOKING: 超售
     * ROOM_MAINTENANCE: 房间维修
     * UPGRADE: 房型升级
     * DOWNGRADE: 房型降级
     * SYSTEM_ERROR: 系统错误
     * PAYMENT_FAILED: 支付失败
     * OTHER: 其他原因
     */
    private String cancelReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 取消备注
     */
    private String remark;

    /**
     * 是否收取取消费用
     */
    private Boolean chargeCancelFee;

    /**
     * 取消费用金额
     */
    private java.math.BigDecimal cancelFeeAmount;

    /**
     * 取消费用说明
     */
    private String cancelFeeRemark;

    /**
     * 房型代码（用于验证）
     */
    private String roomType;

    /**
     * 房间号（如果已分配房间）
     */
    private String roomNo;

    /**
     * 预订人（用于验证）
     */
    private String booker;

    /**
     * 预订电话（用于验证）
     */
    private String phone;

    /**
     * 渠道代码
     */
    private String channelCode;

    /**
     * OTA订单号
     */
    private String otaOrderId;

    @Override
    public Class<CwCrsRoomCancelV1Res> getResponseClass() {
        return CwCrsRoomCancelV1Res.class;
    }
}
