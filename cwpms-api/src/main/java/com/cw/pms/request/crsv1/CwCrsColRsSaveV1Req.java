package com.cw.pms.request.crsv1;

import com.alibaba.fastjson.annotation.JSONField;
import com.cw.common.annotation.CwApiOperation;
import com.cw.common.request.BaseCwApiRequest;
import com.cw.pms.model.GuestInfo;
import com.cw.pms.model.StayDateRange;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DSPMS综合预订请求参数
 * 支持客房预订、餐饮预订、门票预订、导游预订、车船票预订、SPA预订等多种预订类型
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@CwApiOperation("/pmsopen/v1/cwcrs/saveroom")
public class CwCrsColRsSaveV1Req extends BaseCwApiRequest<CwCrsColRsSaveV1Res> {

    /**
     * 预订人
     */
    private String booker;

    /**
     * 预订号码
     */
    private String phone;

    /**
     * 客源代码
     */
    private String source;

    /**
     * 预订类型
     */
    private String resType;


    /**
     * 渠道代码
     */
    private String channelCode;

    /**
     * 市场代码
     */
    private String market;

    /**
     * 第三方与PMS交互的id,由第三方提供
     */
    private String crsColId;


    /**
     * 线下PMS确认id，新建订单为空
     */
    private String pmsColId;

    /**
     * 付款方式
     */
    private String payment;

    /**
     * 套餐代码
     */
    private String kitCode;

    /**
     * 套餐份数
     */
    private String kitCount;

    /**
     * 状态
     */
    private String status;

    /**
     * 区块代码
     */
    private String blockCode;

    /**
     * 到店日期 yyyy-MM-dd
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date arrDate;

    /**
     * 订单结束日期 yyyy-MM-dd
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date deptDate;


    /**
     * 备注
     */
    private String remark;

    /**
     * OTA订单号
     */
    private String otaOrderId;

    /**
     * 总价
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 客人档案信息
     */
    private GuestInfo guestInfo;

    /**
     * 客房订单列表
     */
    private List<Room> rooms = Lists.newArrayList();

    /**
     * 餐饮预订列表
     */
    private List<CateringBooking> cateringBookings = Lists.newArrayList();

    /**
     * 门票预订列表
     */
    private List<Ticket> tickets = Lists.newArrayList();


    /**
     * 其他信息
     */
    private List<Others> others = Lists.newArrayList();

    @Override
    public Class<CwCrsColRsSaveV1Res> getResponseClass() {
        return CwCrsColRsSaveV1Res.class;
    }

    /**
     * 客房预订信息
     */
    @Data
    @NoArgsConstructor
    public static class Room {

        /**
         * 入住日期范围
         */
        private StayDateRange stayDateRange = new StayDateRange();

        /**
         * 区块代码
         */
        private String blockCode;

        /**
         * 订单号
         */
        private String pmsRoomOrderId;

        /**
         * crs 客房订单号
         */
        private String crsRoomOrderId;

        /**
         * 渠道
         */
        private String channel;

        /**
         * 房间数量
         */
        private Integer numberOfRooms;

        /**
         * 市场
         */
        private String market;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 客源
         */
        private String source;

        /**
         * 房价代码
         */
        private String rateCode;

        /**
         * 普通备注
         */
        private String common;

        /**
         * 儿童数
         */
        private Integer children;

        /**
         * 预订类型
         */
        private String bookingType;

        /**
         * 成人数
         */
        private Integer adults;

        /**
         * 特殊备注
         */
        private String specialCommon;

        /**
         * 是否显示房价
         */
        private boolean rateVisible;

        /**
         * 房型
         */
        private String roomType;

        /**
         * 房间号
         */
        private String roomNo;

        /**
         * 合住号
         */
        private String shareNo;

        /**
         * 支付信息
         */
        private PaymentInfoBean paymentInfo;

        /**
         * 房价计划列表
         */
        private List<RatePlanBean> ratePlan = Lists.newArrayList();

        /**
         * 服务包列表
         */
        private List<ServicePackagesBean> servicePackages = Lists.newArrayList();

        /**
         * 入住日期范围信息
         */
        @Data
        @NoArgsConstructor
        public static class StayDateRangeBean {
            /**
             * 开始日期
             */
            @JSONField(format = "yyyy-MM-dd")
            private Date startDate;

            /**
             * 结束日期
             */
            @JSONField(format = "yyyy-MM-dd")
            private Date endDate;
        }

        /**
         * 支付信息
         */
        @Data
        @NoArgsConstructor
        public static class PaymentInfoBean {
            /**
             * 支付类型
             */
            private String paymentType;
        }

        /**
         * 房价计划信息
         */
        @Data
        @NoArgsConstructor
        public static class RatePlanBean {
            /**
             * 日期
             */
            @JSONField(format = "yyyy-MM-dd")
            private Date date;

            /**
             * 金额
             */
            private BigDecimal amount;
        }

        /**
         * 服务包信息
         */
        @Data
        @NoArgsConstructor
        public static class ServicePackagesBean {
            /**
             * 套餐代码
             */
            private String packageCode;
        }
    }

    /**
     * 餐饮预订信息
     */
    @Data
    @NoArgsConstructor
    public static class CateringBooking {
        /**
         * 网络ID
         */
        private String networkId;

        /**
         * 综合预订网络ID
         */
        private String colligateNetworkId;

        /**
         * 桌号/包厢号列表
         */
        private List<String> tableNo = Lists.newArrayList();

        /**
         * 预订人
         */
        private String booker;

        /**
         * 预订电话
         */
        private String phone;

        /**
         * 使用日期
         */
        @JSONField(format = "yyyy-MM-dd")
        private Date useDate;

        /**
         * 餐厅
         */
        private String restaurant;

        /**
         * 餐标代码
         */
        private String standardCode;

        /**
         * 人数
         */
        private Integer num;

        /**
         * 桌数
         */
        private Integer tables;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 餐段
         */
        private String period;

        /**
         * 客源代码
         */
        private String source;

        /**
         * 市场代码
         */
        private String market;

        /**
         * 备注信息
         */
        private String info;
    }

    /**
     * 门票预订信息
     */
    @Data
    @NoArgsConstructor
    public static class Ticket {
        /**
         * 网络ID
         */
        private String networkId;

        /**
         * 使用日期
         */
        @JSONField(format = "yyyy-MM-dd")
        private Date useDate;

        /**
         * 门票代码
         */
        private String ticketCode;

        /**
         * 数量
         */
        private Integer num;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 备注信息
         */
        private String info;
    }


    /**
     * 其他预订项目信息
     */
    @Data
    @NoArgsConstructor
    public static class Others {
        /**
         * 代码
         */
        private String code;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private String num;

        /**
         * 网络ID
         */
        private String networkId;

        /**
         * 信息
         */
        private String info;
    }
}
