package com.cw.pms.response.crsv1;

import com.cw.common.response.BaseCwApiResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DSPMS客房预订取消响应
 * 返回客房取消操作的处理结果和相关信息
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
public class CwCrsRoomCancelV1Res extends BaseCwApiResponse<CwCrsRoomCancelV1Res.BizModel> {

    @Data
    public static class BizModel {
        /**
         * 取消处理结果
         */
        private CancelResult result;

        /**
         * 退款信息
         */
        private RefundInfo refundInfo;
    }

    /**
     * 取消处理结果信息
     */
    @Data
    @NoArgsConstructor
    public static class CancelResult {

        /**
         * CRS客房订单号
         */
        private String crsRoomOrderId;

        /**
         * PMS客房订单号
         */
        private String pmsRoomOrderId;

        /**
         * 处理状态
         */
        private boolean success;

        /**
         * 描述信息
         */
        private String description;

        /**
         * 取消时间
         */
        private Date cancelTime;

        /**
         * 取消费用
         */
        private BigDecimal cancelFee;

        /**
         * 房间号（如果已分配）
         */
        private String roomNo;

        /**
         * 房型代码
         */
        private String roomType;

        /**
         * 原订单状态
         */
        private String originalStatus;

        /**
         * 取消后状态
         */
        private String canceledStatus;
    }

    /**
     * 退款信息
     */
    @Data
    @NoArgsConstructor
    public static class RefundInfo {

        /**
         * 是否需要退款
         */
        private boolean needRefund;

        /**
         * 退款金额
         */
        private BigDecimal refundAmount;

        /**
         * 退款流水号
         */
        private String refundSerialNo;

        /**
         * 退款状态
         * PENDING: 待处理
         * PROCESSING: 处理中
         * SUCCESS: 退款成功
         * FAILED: 退款失败
         */
        private String refundStatus;

        /**
         * 退款说明
         */
        private String refundRemark;

        /**
         * 预计退款到账时间
         */
        private Date estimatedRefundTime;
    }
}
