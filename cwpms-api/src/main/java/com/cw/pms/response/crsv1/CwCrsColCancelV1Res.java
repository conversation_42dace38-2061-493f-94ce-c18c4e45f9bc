package com.cw.pms.response.crsv1;

import com.cw.common.response.BaseCwApiResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DSPMS综合预订取消响应
 * 返回取消操作的处理结果和相关信息
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
public class CwCrsColCancelV1Res extends BaseCwApiResponse<CwCrsColCancelV1Res.BizModel> {

    @Data
    public static class BizModel {
        /**
         * 取消处理结果列表
         */
        private List<CancelResult> results;

        /**
         * 退款信息
         */
        private RefundInfo refundInfo;
    }

    /**
     * 取消处理结果信息
     */
    @Data
    @NoArgsConstructor
    public static class CancelResult {

        /**
         * 中台的订单号
         */
        private String crsno;

        /**
         * PMS订单号
         */
        private String pmsno;

        /**
         * 处理状态
         */
        private boolean success;

        /**
         * 描述信息
         */
        private String description;

        /**
         * 处理类型
         * COLLIGATE：综合订单
         * RESERVATION：客房订单
         * CATERING：餐饮订单
         * TICKET：门票订单
         * GUIDE：导游
         * BOAT：车船票
         * SPA：SPA订单
         */
        private String type;

        /**
         * 取消时间
         */
        private Date cancelTime;

        /**
         * 取消费用
         */
        private BigDecimal cancelFee;
    }

    /**
     * 退款信息
     */
    @Data
    @NoArgsConstructor
    public static class RefundInfo {

        /**
         * 是否需要退款
         */
        private boolean needRefund;

        /**
         * 退款金额
         */
        private BigDecimal refundAmount;

        /**
         * 退款流水号
         */
        private String refundSerialNo;

        /**
         * 退款状态
         * PENDING: 待处理
         * PROCESSING: 处理中
         * SUCCESS: 退款成功
         * FAILED: 退款失败
         */
        private String refundStatus;

        /**
         * 退款说明
         */
        private String refundRemark;
    }
}
