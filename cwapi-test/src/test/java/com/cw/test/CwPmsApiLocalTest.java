package com.cw.test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.cw.common.client.CwApiClient;
import com.cw.pms.model.GuestInfo;
import com.cw.pms.request.*;
import com.cw.pms.request.crsv1.CwCrsColPayV1Req;
import com.cw.pms.response.*;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CwPmsApiLocalTest {

    private CwApiClient client;
    private Date startDate;
    private Date endDate;
    private String roomType = "YZSCF";
    private String rateCode = "SKJ";
    private String deptCode = "1000";
    private String appid = "001";
    private String appsecret = "5bcaqh19ks128q5auw1lo23v6fi7cjht";
    private String token;
    private String paymentCode = "CA";
    private String telno = "13807896787";


    //http://121.43.50.247:9530
    @Before
    public void setup() {
        // 初始化API客户端
        client = new CwApiClient(
                "http://192.168.3.77:9530", // 测试服务器地址
                "001",                 // 测试应用ID
                "5bcaqh19ks128q5auw1lo23v6fi7cjht",            // 测试私钥
                "RSA2"                         // 签名方式
        );

        startDate = new Date();
        endDate = DateUtil.offsetDay(startDate, 1);

    }

    @Test
    public void 测试签名() {
        String appid = "001";

    }

    @Test
    public void testQueryArea() throws Exception {
        CwAreaQueryReq req = new CwAreaQueryReq();
        req.setTenantId("test_tenant");
        CwAreaQueryRes response = client.execute(req);
        System.out.println("Area query response: " + JSON.toJSONString(response));
    }


    @Test
    public void testQueryBlockAllotment() throws Exception {
        CwBlockAllotmentQueryReq req = new CwBlockAllotmentQueryReq();
        req.setBlock("BLOCK001");
        req.setRoomTypes("DLX,STD");
        req.setStartDate("2024-03-01");
        req.setEndDate("2024-03-07");
        CwBlockAllotmentQueryRes response = client.execute(req);
        System.out.println("Block allotment query response: " + JSON.toJSONString(response));
    }

    @Test
    public void testQueryChannel() throws Exception {
        CwChannelQueryReq req = new CwChannelQueryReq();
        CwChannelQueryRes response = client.execute(req);
        System.out.println("Channel query response: " + JSON.toJSONString(response));
    }

    @Test
    public void testSaveRoom() throws Exception {
        CwSaveRoomReq req = new CwSaveRoomReq();
        req.setAdults(2);
        req.setChildren(1);
        req.setBooker("Test Booker");
        req.setBookPhone("1234567890");
        req.setStartDate(DateUtil.formatDate(startDate));
        req.setEndDate(DateUtil.formatDate(endDate));
        req.setRoomType(roomType);
        req.setNum(1);

        GuestInfo guestInfo = new GuestInfo();
        guestInfo.setName("Test Guest");
        req.setGuestInfo(guestInfo);

        CwSaveRoomRes response = client.execute(req);
        System.out.println("Save room response: " + JSON.toJSONString(response));
    }

    @Test
    public void testColPay() throws Exception {
        CwCrsColPayV1Req req = new CwCrsColPayV1Req();
        req.setAmount(new BigDecimal("1000.00"));
        req.setSerialNo("PAY" + System.currentTimeMillis());

        CwColPayRes response = client.execute(req);
        System.out.println("Payment response: " + JSON.toJSONString(response));
    }

    @Test
    public void testCancelRoom() throws Exception {
        CwCancelRoomReq req = new CwCancelRoomReq();
        req.setPmsRoomId("ROOM001");
        req.setReason("Customer requested cancellation");

        CwCancelRoomRes response = client.execute(req);
        System.out.println("Cancel room response: " + JSON.toJSONString(response));
    }

    @Test
    public void testQueryRoomInventory() throws Exception {
        CwRoomInventoryReq req = new CwRoomInventoryReq();
        req.setRoomTypes(roomType);
        req.setStartDate(DateUtil.formatDate(startDate));
        req.setEndDate(DateUtil.formatDate(endDate));

        CwRoomInventoryRes response = client.execute(req);
        System.out.println("Room inventory response: " + JSON.toJSONString(response));
    }

    @Test
    public void testSaveColReservation() throws Exception {
        CwColRsReq req = new CwColRsReq();
        req.setBooker("吴某人");
        req.setPhone(telno);
        req.setNetworkId("DS" + RandomUtil.randomNumbers(9));//中台订单号 (内部平台号)
        req.setOtaOrderId("OTA" + RandomUtil.randomNumbers(9));//小程序 或者说是OTA各大平台的订单号
        req.setArrDate(startDate);
        req.setDeptDate(endDate);

        List<CwColRsReq.Room> rooms = new ArrayList<>();
        CwColRsReq.Room room = new CwColRsReq.Room();
        room.setRoomType("YZSCF");//MYDCF
        room.setNumberOfRooms(1);
        room.setRateCode(rateCode);
        room.setCommon("请安排大床房");
        room.getStayDateRange().setStartDate(startDate);
        room.getStayDateRange().setEndDate(endDate);
        room.setChannel("2");

        rooms.add(room);
        req.setRooms(rooms);

        CwColRsRes response = client.execute(req);

        // 生成curl命令
        String curlCommand = client.generateCurlCommand(req);
        // 输出curl命令
        System.out.println(curlCommand);


        System.out.println("Group reservation response: " + JSON.toJSONString(response));
    }

    @Test
    public void testQueryColReservation() throws Exception {
        CwQueryColReq req = new CwQueryColReq();
        req.setNetworkId("DS934603513");
        req.setOtaorderId("OTA640332382");

        CwQueryColRes response = client.execute(req);
        System.out.println("Group reservation query response: " + JSON.toJSONString(response));

    }

    @Test
    public void testQueryPayment() throws Exception {
        CwPaymentQueryReq req = new CwPaymentQueryReq();
        req.setPaymentCode("CASH");

        CwPaymentQueryRes response = client.execute(req);
        System.out.println("Payment methods response: " + JSON.toJSONString(response));
    }

    @Test
    public void testQueryAccount() throws Exception {
        CwAccountQueryReq req = new CwAccountQueryReq();
        req.setPmsId("ORDER001");

        CwAccountQueryRes response = client.execute(req);
        System.out.println("Account query response: " + JSON.toJSONString(response));
    }

    @Test
    public void testRoomConsumption() throws Exception {
        CwOrderConsumptionReq req = new CwOrderConsumptionReq();
        req.setRoomReservationNo("ROOM001");
        req.setAmount(new BigDecimal("100.00"));
        req.setDeptCode("FOOD");
        req.setRemark("Room service dinner");

        CwOrderConsumptionRes response = client.execute(req);
        System.out.println("Room consumption response: " + JSON.toJSONString(response));
    }
}